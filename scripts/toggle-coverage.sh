#!/bin/bash

# Toggle PCOV coverage on/off for performance
# Usage: ./scripts/toggle-coverage.sh [on|off]

set -e

MODE=${1:-"off"}

if [ "$MODE" != "on" ] && [ "$MODE" != "off" ]; then
    echo "Usage: $0 [on|off]"
    echo "  on  - Enable PCOV coverage"
    echo "  off - Disable PCOV coverage (default)"
    exit 1
fi

echo "🔧 Toggling PCOV coverage: $MODE"

if [ "$MODE" = "on" ]; then
    echo "⚠️  Enabling PCOV coverage (will slow down the application)"
    docker compose exec rentals bash -c "echo 'pcov.enabled=1' > /usr/local/etc/php/conf.d/99-pcov-enabled.ini"
else
    echo "✅ Disabling PCOV coverage (for better performance)"
    docker compose exec rentals bash -c "echo 'pcov.enabled=0' > /usr/local/etc/php/conf.d/99-pcov-enabled.ini"
fi

echo "🔄 Restarting container to apply changes..."
docker compose restart rentals

echo "✅ PCOV coverage is now: $MODE"

# Show current status
echo ""
echo "📊 Current PHP extensions status:"
docker compose exec rentals php -m | grep -E "(xdebug|pcov)" || echo "No debug extensions found"
