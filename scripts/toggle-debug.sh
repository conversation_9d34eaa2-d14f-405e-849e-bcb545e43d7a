#!/bin/bash

# Toggle Xdebug debug mode on/off for performance
# Usage: ./scripts/toggle-debug.sh [on|off]

set -e

MODE=${1:-"off"}

if [ "$MODE" != "on" ] && [ "$MODE" != "off" ]; then
    echo "Usage: $0 [on|off]"
    echo "  on  - Enable Xdebug debug mode"
    echo "  off - Disable Xdebug debug mode (default)"
    exit 1
fi

echo "🔧 Toggling Xdebug debug mode: $MODE"

if [ "$MODE" = "on" ]; then
    echo "⚠️  Enabling Xdebug debug mode (will slow down the application)"
    docker compose exec rentals bash -c "echo 'xdebug.mode=debug' > /usr/local/etc/php/conf.d/99-xdebug-mode.ini"
else
    echo "✅ Disabling Xdebug debug mode (for better performance)"
    docker compose exec rentals bash -c "echo 'xdebug.mode=off' > /usr/local/etc/php/conf.d/99-xdebug-mode.ini"
fi

echo "🔄 Restarting container to apply changes..."
docker compose restart rentals

echo "✅ Xdebug debug mode is now: $MODE"

# Show current status
echo ""
echo "📊 Current PHP extensions status:"
docker compose exec rentals php -m | grep -E "(xdebug|pcov)" || echo "No debug extensions found"
