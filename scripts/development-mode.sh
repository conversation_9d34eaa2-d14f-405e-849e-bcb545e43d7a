#!/bin/bash

# Enable development mode with debugging tools
# Usage: ./scripts/development-mode.sh

set -e

echo "🛠️  Enabling development mode..."
echo "   - Enabling Xdebug debug mode"
echo "   - Enabling Laravel Debugbar"
echo "   - PCOV remains disabled (use coverage scripts for testing)"

# Enable Xdebug debug mode
docker compose exec rentals bash -c "echo 'xdebug.mode=debug' > /usr/local/etc/php/conf.d/99-xdebug-mode.ini"

# Enable Laravel Debugbar by setting APP_DEBUG=true
docker compose exec rentals bash -c "sed -i 's/APP_DEBUG=false/APP_DEBUG=true/' /var/www/html/.env"

echo "🔄 Restarting container to apply changes..."
docker compose restart rentals

echo "✅ Development mode enabled!"
echo ""
echo "📊 Current status:"
echo "   Xdebug mode: debug"
echo "   PCOV coverage: disabled (use coverage scripts when needed)"
echo "   Laravel Debugbar: enabled"
echo ""
echo "💡 To return to performance mode: ./scripts/performance-mode.sh"
echo "💡 To enable coverage for testing: ./scripts/toggle-coverage.sh on"
echo "💡 To run tests with coverage: composer test-coverage-html"
echo ""
echo "🚨 Note: Development mode will slow down the application"
echo "   Use performance mode for normal development work"
