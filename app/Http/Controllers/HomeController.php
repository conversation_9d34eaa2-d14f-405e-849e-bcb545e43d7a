<?php namespace App\Http\Controllers;

use App\Coupon;
use App\Category;
use App\GoogleReview;
use Illuminate\Support\Facades\Request;
use Spatie\SchemaOrg\Schema;

class HomeController extends Controller {


    public function __construct()
    {
        parent::__construct();

        // canonical url initialization
        $this->view_data['canonical_url'] = Request::url();
    }

    public function getIndex()
    {
        $featuredListings = array();
        $featuredCategory = Category::where(['name' => 'featured'])->first();

        if($featuredCategory)
        {
            $featuredListings = $featuredCategory->publishedListings()->getResults();
        }
        $this->view_data['featuredListings'] = $featuredListings;

        // fetch all valid coupons (enabled, promoted and belonging to eurodollar site)
        $coupons = Coupon::eurodollarPromoted()->get();

        $this->view_data['coupons'] = $coupons;

        // fetch a testimonial in radnom order
        // TODO: remove code duplication (also loaded in the view composer paired with the partial blade view)
        $this->view_data['testimonial_review'] = GoogleReview::testimoniable()
            ->inRandomOrder()
            ->first();

        // json-ld microdata script (echoed in view)
        $this->view_data['local_business_schema'] = Schema::localBusiness()
            ->url(route('home'))
            ->logo(asset('images/logo.svg'))
            ->image(asset('images/logo.svg'))
            ->name(trans('common.site_name'))
            ->email(config('schema_org.local_business.email_address'))
            ->priceRange(config('schema_org.local_business.price_range'))
            ->hasMap(config('schema_org.local_business.google_maps_place'))
            ->address(Schema::postalAddress()
                ->addressCountry(config('schema_org.local_business.address.country'))
                ->addressRegion(config('schema_org.local_business.address.region'))
                ->addressLocality(config('schema_org.local_business.address.locality'))
                ->postalCode(config('schema_org.local_business.address.postal_code'))
                ->streetAddress(config('schema_org.local_business.address.street_address'))
            )
            ->telephone(trans('common.phone'))
            ->openingHours(config('schema_org.local_business.opening_hours'))
            ->aggregateRating(Schema::aggregateRating()
                ->bestRating(config('schema_org.local_business.aggregate_rating.best'))
                ->worstRating(config('schema_org.local_business.aggregate_rating.worst'))
                ->ratingCount(config('schema_org.local_business.aggregate_rating.rating_count'))
                ->ratingValue(config('schema_org.local_business.aggregate_rating.rating_value'))
            );

        // open graph
        $this->overwriteOpengraph(
            trans('home.page_title'),
            trans('home.meta_description')
        );

        return view('sansa.home.main', $this->view_data);
    }

}
